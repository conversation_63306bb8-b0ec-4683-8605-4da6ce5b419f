import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  BarChart3,
  Bot,
  CheckCircle,
  Clock,
  Database,
  Download,
  FileText,
  Globe,
  MapPin,
  Network,
  Shield,
  TrendingUp,
  Upload,
} from 'lucide-react';
import React from 'react';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../@/components/ui/Common/Elements/Card/Card';
import { Progress } from '../../../@/components/ui/Common/Elements/ProgressBar/ProgressBar';
import { Badge } from '../../../@/components/ui/badge';
import { CardDescription } from '../../../@/components/ui/card';

interface AnalyticsTabProps {}

const AnalyticsTab: React.FC<AnalyticsTabProps> = () => {
  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-4 mt-6">
        <Card className="p-3">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Consent Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">87.3%</div>
            <p className="text-xs text-muted-foreground">+2.1% from last month</p>
          </CardContent>
        </Card>
        <Card className="p-3">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Opt-out Rate</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">12.7%</div>
            <p className="text-xs text-muted-foreground">-0.8% from last month</p>
          </CardContent>
        </Card>
        <Card className="p-3">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Template Performance</CardTitle>
            <BarChart3 className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">94.2%</div>
            <p className="text-xs text-muted-foreground">Avg completion rate</p>
          </CardContent>
        </Card>
        <Card className="p-3">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Compliance Score</CardTitle>
            <Shield className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">98.7%</div>
            <p className="text-xs text-muted-foreground">Cross-framework average</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* Consent Funnel Analytics */}
        <Card className="px-2 py-4">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-xl">
              <TrendingUp className="h-6 w-6 text-primary" />
              Consent Funnel Analysis
            </CardTitle>
            <CardDescription>Track user journey through consent collection process</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Form Views</span>
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-bold">45,892</span>
                    <Badge variant="outline">100%</Badge>
                  </div>
                </div>
                <Progress value={100} className="h-3" />
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Form Starts</span>
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-bold">41,203</span>
                    <Badge variant="secondary">89.8%</Badge>
                  </div>
                </div>
                <Progress value={89.8} className="h-3" />
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Completed Forms</span>
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-bold">38,847</span>
                    <Badge className="text-white" variant="default">
                      84.6%
                    </Badge>
                  </div>
                </div>
                <Progress value={84.6} className="h-3" />
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Consent Given</span>
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-bold">33,905</span>
                    <Badge className="bg-green-100 text-green-800">73.9%</Badge>
                  </div>
                </div>
                <Progress value={73.9} className="h-3" />
              </div>
            </div>

            <div className="mt-6 border-t pt-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">10.2%</div>
                  <div className="text-sm text-muted-foreground">Drop-off Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-custom-primary">2.4min</div>
                  <div className="text-sm text-muted-foreground">Avg Time to Consent</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Channel Performance */}
        <Card className="px-2 py-4">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-xl">
              <Network className="h-6 w-6 text-primary" />
              Multi-Channel Performance
            </CardTitle>
            <CardDescription>
              Consent collection performance across different channels
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="grid grid-cols-1 gap-4">
                <div className="rounded-lg border p-4">
                  <div className="mb-3 flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Globe className="h-5 w-5 text-blue-600" />
                      <span className="font-medium">Web Forms</span>
                    </div>
                    <Badge className="bg-blue-100 text-blue-800">Primary</Badge>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="font-semibold">18,450</div>
                      <div className="text-muted-foreground">Submissions</div>
                    </div>
                    <div>
                      <div className="font-semibold text-green-600">89.2%</div>
                      <div className="text-muted-foreground">Consent Rate</div>
                    </div>
                    <div>
                      <div className="font-semibold">2.1min</div>
                      <div className="text-muted-foreground">Avg Time</div>
                    </div>
                  </div>
                </div>

                <div className="rounded-lg border p-4">
                  <div className="mb-3 flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Database className="h-5 w-5 text-green-600" />
                      <span className="font-medium">API Integration</span>
                    </div>
                    <Badge className="bg-green-100 text-green-800">Automated</Badge>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="font-semibold">12,205</div>
                      <div className="text-muted-foreground">Submissions</div>
                    </div>
                    <div>
                      <div className="font-semibold text-green-600">95.7%</div>
                      <div className="text-muted-foreground">Consent Rate</div>
                    </div>
                    <div>
                      <div className="font-semibold">0.3s</div>
                      <div className="text-muted-foreground">Processing</div>
                    </div>
                  </div>
                </div>

                <div className="rounded-lg border p-4">
                  <div className="mb-3 flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Upload className="h-5 w-5 text-orange-600" />
                      <span className="font-medium">Manual Upload</span>
                    </div>
                    <Badge className="bg-orange-100 text-orange-800">Batch</Badge>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="font-semibold">3,150</div>
                      <div className="text-muted-foreground">Records</div>
                    </div>
                    <div>
                      <div className="font-semibold text-green-600">78.4%</div>
                      <div className="text-muted-foreground">Consent Rate</div>
                    </div>
                    <div>
                      <div className="font-semibold">15min</div>
                      <div className="text-muted-foreground">Processing</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Advanced Analytics */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        {/* Geographic Analytics */}
        <Card className="px-2 py-4">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <MapPin className="h-5 w-5 text-primary" />
              Geographic Insights
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                  <span className="text-sm">European Union</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">45.2%</div>
                  <div className="text-xs text-muted-foreground">GDPR</div>
                </div>
              </div>
              <Progress value={45.2} className="h-2" />
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-green-500"></div>
                  <span className="text-sm">United States</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">32.1%</div>
                  <div className="text-xs text-muted-foreground">CCPA</div>
                </div>
              </div>
              <Progress value={32.1} className="h-2" />
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-orange-500"></div>
                  <span className="text-sm">Asia Pacific</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">15.8%</div>
                  <div className="text-xs text-muted-foreground">Mixed</div>
                </div>
              </div>
              <Progress value={15.8} className="h-2" />
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-purple-500"></div>
                  <span className="text-sm">Other Regions</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">6.9%</div>
                  <div className="text-xs text-muted-foreground">Various</div>
                </div>
              </div>
              <Progress value={6.9} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* Template Analytics */}
        <Card className="px-2 py-4">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <FileText className="h-5 w-5 text-primary" />
              Template Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="rounded-lg border p-3">
                <div className="mb-2 flex items-center justify-between">
                  <span className="text-sm font-medium">Marketing Template</span>
                  <Badge className="bg-green-100 text-green-800">Top Performer</Badge>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <div className="font-semibold">92.4%</div>
                    <div className="text-muted-foreground">Consent Rate</div>
                  </div>
                  <div>
                    <div className="font-semibold">18,450</div>
                    <div className="text-muted-foreground">Submissions</div>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border p-3">
                <div className="mb-2 flex items-center justify-between">
                  <span className="text-sm font-medium">Analytics Template</span>
                  <Badge variant="secondary">Good</Badge>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <div className="font-semibold">87.1%</div>
                    <div className="text-muted-foreground">Consent Rate</div>
                  </div>
                  <div>
                    <div className="font-semibold">12,830</div>
                    <div className="text-muted-foreground">Submissions</div>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border p-3">
                <div className="mb-2 flex items-center justify-between">
                  <span className="text-sm font-medium">Support Template</span>
                  <Badge variant="outline">Needs Review</Badge>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <div className="font-semibold">71.3%</div>
                    <div className="text-muted-foreground">Consent Rate</div>
                  </div>
                  <div>
                    <div className="font-semibold">8,920</div>
                    <div className="text-muted-foreground">Submissions</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Predictive Analytics */}
        <Card className="px-2 py-4">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Bot className="h-5 w-5 text-primary" />
              AI Insights
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                <div className="flex items-start gap-3">
                  <TrendingUp className="mt-0.5 h-5 w-5 text-blue-600" />
                  <div>
                    <h4 className="text-sm font-semibold text-blue-900">
                      Optimization Opportunity
                    </h4>
                    <p className="mt-1 text-xs text-blue-700">
                      Marketing template could see 15% improvement with form redesign
                    </p>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                <div className="flex items-start gap-3">
                  <CheckCircle className="mt-0.5 h-5 w-5 text-green-600" />
                  <div>
                    <h4 className="text-sm font-semibold text-green-900">Peak Performance</h4>
                    <p className="mt-1 text-xs text-green-700">
                      API integration showing best consent rates this quarter
                    </p>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border border-orange-200 bg-orange-50 p-4">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="mt-0.5 h-5 w-5 text-orange-600" />
                  <div>
                    <h4 className="text-sm font-semibold text-orange-900">Renewal Alert</h4>
                    <p className="mt-1 text-xs text-orange-700">
                      3,247 consents expiring in next 30 days
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <Button variant="outline" className="w-full font-medium">
              <BarChart3 className="mr-2 h-4 w-4" />
              View Full Analytics
            </Button>
          </CardContent>
        </Card>
      </div>
      <Card className="px-2 py-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-xl">
            <Download className="h-6 w-6 text-primary" />
            Analytics Reports & Export
          </CardTitle>
          <CardDescription>
            Generate comprehensive analytics reports for stakeholders and compliance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Button className="flex h-auto flex-col items-center gap-3 p-6" variant="outline">
              <FileText className="h-8 w-8 text-primary" />
              <div className="text-center">
                <div className="font-semibold">Executive Summary</div>
                <div className="text-sm text-muted-foreground">High-level metrics</div>
              </div>
            </Button>
            <Button className="flex h-auto flex-col items-center gap-3 p-6" variant="outline">
              <BarChart3 className="h-8 w-8 text-primary" />
              <div className="text-center">
                <div className="font-semibold">Detailed Analytics</div>
                <div className="text-sm text-muted-foreground">Full breakdown</div>
              </div>
            </Button>
            <Button className="flex h-auto flex-col items-center gap-3 p-6" variant="outline">
              <Globe className="h-8 w-8 text-primary" />
              <div className="text-center">
                <div className="font-semibold">Regional Report</div>
                <div className="text-sm text-muted-foreground">By jurisdiction</div>
              </div>
            </Button>
            <Button className="flex h-auto flex-col items-center gap-3 p-6" variant="outline">
              <Clock className="h-8 w-8 text-primary" />
              <div className="text-center">
                <div className="font-semibold">Trend Analysis</div>
                <div className="text-sm text-muted-foreground">Historical data</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AnalyticsTab;
