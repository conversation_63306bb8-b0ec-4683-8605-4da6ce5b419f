import {
  Building,
  Calendar,
  Circle,
  Clock,
  Code,
  Database,
  Edit,
  Eye,
  FileText,
  Key,
  LinkIcon,
  Settings,
  Users,
  Workflow,
} from 'lucide-react';
import React from 'react';
import { Badge } from '../../../@/components/ui/badge';

import { useQuery } from '@tanstack/react-query';
import { useSelector } from 'react-redux';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../@/components/ui/Common/Elements/Card/Card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../../@/components/ui/Common/Elements/Select/DropDownMenu';
import { RootState } from '../../../redux/store';
import { convertDateToHumanView } from '../../common/CommonHelperFunctions';
import { ChartCardLoader } from '../../common/LoadingUI';
import { change_collection_template, get_ucm_collection_builder_detailed_dashboard_data } from '../../common/services/universal-consent-management';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
const collectionTemplates = [
  {
    id: 1,
    name: 'Newsletter Signup',
    description: 'Marketing newsletter subscription form',
    entity: 'Acme Corp',
    department: 'Marketing',
    vendor: 'Mailchimp',
    process: 'Email Marketing',
    subjectIdentity: 'Email Address',
    owner: 'John Doe',
    status: 'Active',
    mappingCount: 5,
    sources: ['Form', 'API'],
    createdDate: '2024-01-15',
    lastModified: '2024-01-20',
  },
  {
    id: 2,
    name: 'Customer Onboarding',
    description: 'New customer registration and data collection',
    entity: 'Acme Corp',
    department: 'Sales',
    vendor: 'Salesforce',
    process: 'Customer Management',
    subjectIdentity: 'Email Address',
    owner: 'Jane Smith',
    status: 'Active',
    mappingCount: 12,
    sources: ['Form', 'API', 'Manual Upload'],
    createdDate: '2024-01-10',
    lastModified: '2024-01-18',
  },
  {
    id: 3,
    name: 'Analytics Tracking',
    description: 'Website and app analytics consent collection',
    entity: 'Acme Corp',
    department: 'Product',
    vendor: 'Google Analytics',
    process: 'Analytics',
    subjectIdentity: 'IP Address',
    owner: 'Mike Johnson',
    status: 'Draft',
    mappingCount: 3,
    sources: ['API'],
    createdDate: '2024-01-12',
    lastModified: '2024-01-19',
  },
];
const consentPurposes = [
  {
    id: 1,
    name: 'Marketing Email Consent',
    description: 'We would like to send you marketing emails about our products and services',
    processingPurposeId: 1,
    processingPurpose: 'Email Marketing',
    expiryDays: 365,
    status: 'Active',
  },
  {
    id: 2,
    name: 'SMS Marketing Consent',
    description: 'We would like to send you promotional SMS messages',
    processingPurposeId: 2,
    processingPurpose: 'SMS Marketing',
    expiryDays: 180,
    status: 'Active',
  },
  {
    id: 3,
    name: 'Analytics Tracking Consent',
    description: 'We would like to track your website usage for analytics purposes',
    processingPurposeId: 3,
    processingPurpose: 'Website Analytics',
    expiryDays: 730,
    status: 'Active',
  },
];

const piiLabels = [
  { id: 1, name: 'Email Address', uniquelyIdentifiable: true, description: 'User email address' },
  { id: 2, name: 'Phone Number', uniquelyIdentifiable: true, description: 'User phone number' },
  { id: 3, name: 'Full Name', uniquelyIdentifiable: false, description: 'User full name' },
  { id: 4, name: 'Address', uniquelyIdentifiable: false, description: 'User postal address' },
  {
    id: 5,
    name: 'Date of Birth',
    uniquelyIdentifiable: false,
    description: 'User date of birth',
  },
  { id: 6, name: 'IP Address', uniquelyIdentifiable: false, description: 'User IP address' },
];

interface CollectionTemplate {
  id: number;
  name: string;
  description: string;
  entity: string;
  department: string;
  vendor: string;
  process: string;
  subjectIdentity: string;
  owner: string;
  status: string;
  mappingCount: number;
  sources: string[];
  createdDate: string;
  lastModified: string;
  active_status: boolean;
  entity_name: string;
  owner_name: string;
  subject_identity_type_name: string;
  created_at: string;
  updated_at: string;
}

interface ConsentPurpose {
  id: number;
  name: string;
  description: string;
  processingPurposeId: number;
  processingPurpose: string;
  expiryDays: number;
  status: string;
}

interface PiiLabel {
  id: number;
  name: string;
  description: string;
  uniquelyIdentifiable: boolean;
}
interface MetricsData {
  common_metrics: {
    total_consents: ConsentMetric;
    active_consents: ConsentMetric;
    declined_consents: ConsentMetric;
    withdrawal_consents: ConsentMetric;
    renewal_consents: ConsentMetric;
  };
  collection_template_metrics: CollectionTemplateMetrics;
}

interface ConsentMetric {
  total_value: number;
  current_month_value: number;
  percentage_change_from_last_month: number;
}

interface CollectionTemplateMetrics {
  customer_id: number;
  entity_id: number;
  total_templates: number;
  active_templates: number;
  inactive_templates: number;
  unique_owners: number;
  total_consent_source: number;
  all_filtered_mapping_count: number;
  active_ct_filtered_mapping_count: number;
  inactive_ct_filtered_mapping_count: number;
  total_mapping_count: number;
  active_ct_total_mapping_count: number;
  inactive_ct_total_mapping_count: number;
}

const CollectionTemplatesTab: React.FC<{
  collectionBuilderDashboardData: MetricsData;
  selectedEntityId: string;
  isLoadingCollectionBuilderDashboardData: boolean;
}> = ({
  collectionBuilderDashboardData,
  selectedEntityId,
  isLoadingCollectionBuilderDashboardData,
}) => {
  const customer_id: number | undefined = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );
  const {t} = useTranslation()

  const fetchCollectionBuilderDashboardData = async () => {
    const response = await get_ucm_collection_builder_detailed_dashboard_data(
      customer_id,
      Number(selectedEntityId)
    );
    console.log(response.result.data, 'response123');
    return response.result.data;
  };

  const {
    data: collectionBuilderDetailedDashboardData,
    isLoading: isLoadingCollectionBuilderDetailedDashboardData,
  } = useQuery({
    queryKey: ['collectionBuilderDetailsDashboardData', customer_id, selectedEntityId],
    queryFn: () => fetchCollectionBuilderDashboardData(),
    enabled: !!customer_id,
  });

  console.log(collectionBuilderDetailedDashboardData, 'collectionBuilderDetailedDashboardData');

  // Handler

  const handleStatusChange = async (status: boolean, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent row click handler from being executed

    try {
      // Make the API call to update the status
      const requestBody = {
        record_id: collectionTemplateId,
        customer_id: customer_id,
        active_status: status,
      };

      const response = await change_collection_template(requestBody);
      if (response.success) {
        toast.success(
          t(
            status
              ? 'ToastMessages.General.TemplateActivatedSuccessfully'
              : 'ToastMessages.General.TemplateInactivatedSuccessfully'
          )
        );
        setReloadCollectionTemplate((prev) => !prev);
        // Optionally refresh the table data or set state to reflect changes
      } else {
        throw new Error(response.data.message || 'Failed to update status');
      }
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.failedToUpdateStatus'));
    }
  };

  return (
    <div className="space-y-6">
      {/* Template Statistics */}
      <div className="mt-6 grid grid-cols-1 gap-4 md:grid-cols-4">
        {isLoadingCollectionBuilderDashboardData ? (
          <div className="flex items-center justify-center">
            <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
          </div>
        ) : (
          <Card className="p-3">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Templates</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {collectionBuilderDashboardData?.collection_template_metrics?.total_templates ??
                  '-'}
              </div>
              <p className="text-xs text-muted-foreground">
                {collectionBuilderDashboardData?.collection_template_metrics?.active_templates ??
                  '-'}{' '}
                active
              </p>
            </CardContent>
          </Card>
        )}

        {isLoadingCollectionBuilderDashboardData ? (
          <div className="flex items-center justify-center">
            <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
          </div>
        ) : (
          <Card className="p-3">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Mappings</CardTitle>
              <LinkIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {collectionBuilderDashboardData?.collection_template_metrics
                  ?.all_filtered_mapping_count ?? '-'}
              </div>
              <p className="text-xs text-muted-foreground">Across all templates</p>
            </CardContent>
          </Card>
        )}

        {isLoadingCollectionBuilderDashboardData ? (
          <div className="flex items-center justify-center">
            <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
          </div>
        ) : (
          <Card className="p-3">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Collection Sources</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {collectionBuilderDashboardData?.collection_template_metrics
                  ?.total_consent_source ?? '-'}
              </div>
              <p className="text-xs text-muted-foreground">Unique source types</p>
            </CardContent>
          </Card>
        )}
        {isLoadingCollectionBuilderDashboardData ? (
          <div className="flex items-center justify-center">
            <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
          </div>
        ) : (
          <Card className="p-3">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Template Owners</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {collectionBuilderDashboardData?.collection_template_metrics?.unique_owners ?? '-'}
              </div>
              <p className="text-xs text-muted-foreground">Responsible stakeholders</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Enhanced Collection Templates Grid */}
      <div className="ml-2 flex items-center justify-start gap-6">
        <div className="flex gap-2 text-sm font-semibold">
          <span>
            <Circle className="inline size-4 fill-green-500 text-green-500" />
          </span>
          <span>Completed</span>
        </div>
        <div className="flex gap-2 text-sm font-semibold">
          <span>
            <Circle className="inline size-4 fill-black" />
          </span>
          <span>In Progress</span>
        </div>
        <div className="flex gap-2 text-sm font-semibold">
          <span>
            <Circle className="inline size-4 fill-white" />
          </span>
          <span>Not Started</span>
        </div>
      </div>
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
        {isLoadingCollectionBuilderDetailedDashboardData ? (
          <div className="flex w-[100vw] items-center justify-center">
            <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
          </div>
        ) : (
          <>
            {collectionBuilderDetailedDashboardData?.map((template: any) => (
              <Card
                key={template?.id}
                className="border-l-primary/20 border-l-4 px-2 py-4 transition-all duration-300 hover:border-l-primary hover:shadow-lg"
              >
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="mb-2 flex items-center gap-2 text-xl">
                        <FileText className="h-5 w-5 text-primary" />
                        {template?.template_name ?? '-'}
                      </CardTitle>
                      <Badge
                        variant={template.active_status === true ? 'default' : 'secondary'}
                        className={template.active_status === true ? 'text-white' : 'text-black'}
                      >
                        {template?.active_status ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    <div className="flex gap-1">
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <CardDescription className="text-sm leading-relaxed">
                    {/* {template.description} */}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Template Details Grid */}
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="space-y-1">
                      <span className="flex items-center gap-1 text-muted-foreground">
                        <Building className="h-3 w-3" />
                        Entity
                      </span>
                      <div className="font-medium text-foreground">
                        {template?.entity_name ?? '-'}
                      </div>
                    </div>

                    <div className="space-y-1">
                      <span className="flex items-center gap-1 text-muted-foreground">
                        <LinkIcon className="h-3 w-3" />
                        Mappings
                      </span>
                      <div className="font-medium text-foreground">4</div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="space-y-1">
                      <span className="flex items-center gap-1 text-muted-foreground">
                        <Users className="h-3 w-3" />
                        Owner Name
                      </span>
                      <div className="font-medium text-foreground">
                        {template?.owner_name ?? '-'}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <span className="flex items-center gap-1 text-muted-foreground">
                        <Users className="h-3 w-3" />
                        Owner Email
                      </span>
                      <div className="font-medium text-foreground">
                        {template?.owner_email ?? '-'}
                      </div>
                    </div>
                  </div>

                  {/* Subject Identity */}
                  <div className="space-y-2">
                    <span className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Key className="h-3 w-3" />
                      Subject Identity
                    </span>
                    <Badge variant="outline" className="w-fit">
                      {template?.pii_name ?? '-'}
                    </Badge>
                  </div>

                  {/* Sources */}
                  <div className="space-y-2">
                    <span className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Database className="h-3 w-3" />
                      Collected Data Sources
                    </span>
                    <div className="flex flex-wrap gap-1">
                      {template?.consent_sources?.length > 0 ? (
                        template?.consent_sources?.map((source: any) => (
                          <Badge key={source} variant="secondary" className="text-xs">
                            {source}
                          </Badge>
                        ))
                      ) : (
                        <>-</>
                      )}
                    </div>
                  </div>

                  {/* Template Metadata */}
                  <div className="border-t border-border/50 pt-3">
                    <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
                      <div>
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          Created: {convertDateToHumanView(template?.created_at) ?? '-'}
                        </span>
                      </div>
                      <div>
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          Modified: {convertDateToHumanView(template?.updated_at) ?? '-'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="grid grid-cols-2 gap-2">
                    <DropdownMenu>
                      <DropdownMenuTrigger>
                        <Button size="sm" variant="outline" className="h-9 font-medium">
                          <Settings className="mr-1 h-3 w-3" />
                          Configure
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="font-primary-text">
                        <DropdownMenuItem onClick={(event) => handleStatusChange(true, event)}>
                          Activate
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(event) => handleStatusChange(false, event)}>
                          Inactivate
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>

                    <Button size="sm" variant="outline" className="h-9 font-medium">
                      <Code className="mr-1 h-3 w-3" />
                      Get Code
                    </Button>
                  </div>

                  {/* Template Workflow Progress */}
                  <div className="space-y-5">
                    <span className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Workflow className="h-3 w-3" />
                      Configuration Progress
                    </span>
                    <div className="flex items-center justify-around">
                      <div className="flex flex-col items-center text-center">
                        <Circle
                          className={`h-6 w-6 ${template?.basic_step === 'completed' ? 'fill-green-500 text-green-500' : template?.basic_step === 'not_started' ? 'fill-white' : 'fill-black text-black'}`}
                        />
                        <span className="mt-1 text-xs text-muted-foreground">Basic & Mapping</span>
                      </div>
                      <div className="flex flex-col items-center text-center">
                        <Circle
                          className={`h-6 w-6 ${template?.retention_and_source === 'completed' ? 'fill-green-500 text-green-500' : template?.retention_and_source === 'not_started' ? 'fill-white' : 'fill-black text-black'}`}
                        />
                        <span className="mt-1 text-xs text-muted-foreground">
                          Retention & Source
                        </span>
                      </div>
                      <div className="flex flex-col items-center text-center">
                        <Circle
                          className={`h-6 w-6 ${template?.user_communication === 'completed' ? 'fill-green-500 text-green-500' : template?.user_communication === 'not_started' ? 'fill-white' : 'fill-black text-black'}`}
                        />
                        <span className="mt-1 text-xs text-muted-foreground">
                          User Communication
                        </span>
                      </div>
                      <div className="flex flex-col items-center text-center">
                        <Circle
                          className={`h-6 w-6 ${template?.localization_setup === 'completed' ? 'fill-green-500 text-green-500' : template?.localization_setup === 'not_started' ? 'fill-white' : 'fill-black text-black'}`}
                        />
                        <span className="mt-1 text-xs text-muted-foreground">Localization</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </>
        )}
      </div>
    </div>
  );
};

export default CollectionTemplatesTab;
